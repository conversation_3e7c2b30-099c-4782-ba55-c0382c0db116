import { useState } from 'react';
import { useApp } from '../context/AppContext';

const taskStatuses = [
  { id: 'pending', name: 'Pendiente', color: '#f39c12', icon: '⏳' },
  { id: 'in-progress', name: 'En Progreso', color: '#3498db', icon: '🔄' },
  { id: 'review', name: 'En Revisión', color: '#9b59b6', icon: '🔍' },
  { id: 'completed', name: 'Completad<PERSON>', color: '#27ae60', icon: '✅' }
];

const taskPriorities = [
  { id: 'low', name: 'Baja', color: '#7f8c8d', icon: '⬇️' },
  { id: 'medium', name: 'Media', color: '#f39c12', icon: '➡️' },
  { id: 'high', name: 'Alta', color: '#e74c3c', icon: '⬆️' }
];

export default function TaskBoard() {
  const { state, actions } = useApp();
  const { tasks, agents, currentAgent } = state;
  
  const [isCreatingTask, setIsCreatingTask] = useState(false);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    priority: 'medium',
    status: 'pending',
    assignedTo: '',
    dueDate: ''
  });
  const [errors, setErrors] = useState({});
  const [editingTask, setEditingTask] = useState(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [filterAssignee, setFilterAssignee] = useState('all');
  const [viewMode, setViewMode] = useState('kanban'); // kanban or list
  
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewTask(prev => ({
      ...prev,
      [name]: value
    }));
    
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };
  
  const validateTaskForm = () => {
    const newErrors = {};
    
    if (!newTask.title.trim()) {
      newErrors.title = 'El título es requerido';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleCreateTask = (e) => {
    e.preventDefault();
    
    if (!validateTaskForm()) {
      return;
    }
    
    actions.createTask(newTask);
    setNewTask({
      title: '',
      description: '',
      priority: 'medium',
      status: 'pending',
      assignedTo: '',
      dueDate: ''
    });
    setIsCreatingTask(false);
  };
  
  const handleUpdateTaskStatus = (taskId, newStatus) => {
    actions.updateTask(taskId, { status: newStatus });
  };
  
  const handleEditTask = (task) => {
    setEditingTask(task);
    setNewTask({
      title: task.title,
      description: task.description || '',
      priority: task.priority || 'medium',
      status: task.status || 'pending',
      assignedTo: task.assignedTo || '',
      dueDate: task.dueDate || ''
    });
  };
  
  const handleSaveTaskChanges = (e) => {
    e.preventDefault();
    
    if (!validateTaskForm()) {
      return;
    }
    
    actions.updateTask(editingTask.id, newTask);
    setEditingTask(null);
  };
  
  const handleDeleteTask = (taskId) => {
    if (confirm('¿Estás seguro de eliminar esta tarea?')) {
      actions.deleteTask(taskId);
    }
  };
  
  const handleAssignTask = (taskId, agentId) => {
    actions.updateTask(taskId, { assignedTo: agentId });
  };
  
  const getFilteredTasks = () => {
    return tasks.filter(task => {
      if (filterStatus !== 'all' && task.status !== filterStatus) return false;
      if (filterPriority !== 'all' && task.priority !== filterPriority) return false;
      if (filterAssignee !== 'all' && task.assignedTo !== filterAssignee) return false;
      return true;
    });
  };
  
  const getStatusTasks = (status) => {
    return getFilteredTasks().filter(task => task.status === status);
  };
  
  const getAgentName = (agentId) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : 'No asignado';
  };
  
  const getAgentAvatar = (agentId) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.avatar : '👤';
  };
  
  const canEditTask = (task) => {
    return currentAgent.role === 'coordinator' || 
           task.createdBy === currentAgent.id || 
           task.assignedTo === currentAgent.id;
  };
  
  return (
    <div className="task-board">
      <div className="task-board-header">
        <h1>✅ Gestión de Tareas</h1>
        <div className="task-board-controls">
          <div className="view-toggle">
            <button
              className={`view-btn ${viewMode === 'kanban' ? 'active' : ''}`}
              onClick={() => setViewMode('kanban')}
            >
              🗳️ Kanban
            </button>
            <button
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              📋 Lista
            </button>
          </div>
          
          {(currentAgent.role === 'coordinator' || currentAgent.role === 'manager') && (
            <button 
              className="create-task-btn"
              onClick={() => setIsCreatingTask(true)}
            >
              ➕ Nueva Tarea
            </button>
          )}
        </div>
      </div>
      
      <div className="task-filters">
        <div className="filter-group">
          <label>Estado:</label>
          <select 
            value={filterStatus} 
            onChange={e => setFilterStatus(e.target.value)}
          >
            <option value="all">Todos</option>
            {taskStatuses.map(status => (
              <option key={status.id} value={status.id}>{status.name}</option>
            ))}
          </select>
        </div>
        
        <div className="filter-group">
          <label>Prioridad:</label>
          <select 
            value={filterPriority} 
            onChange={e => setFilterPriority(e.target.value)}
          >
            <option value="all">Todas</option>
            {taskPriorities.map(priority => (
              <option key={priority.id} value={priority.id}>{priority.name}</option>
            ))}
          </select>
        </div>
        
        <div className="filter-group">
          <label>Asignado a:</label>
          <select 
            value={filterAssignee} 
            onChange={e => setFilterAssignee(e.target.value)}
          >
            <option value="all">Todos</option>
            {agents.map(agent => (
              <option key={agent.id} value={agent.id}>{agent.name} ({agent.role})</option>
            ))}
          </select>
        </div>
      </div>
      
      {viewMode === 'kanban' ? (
        <div className="kanban-board">
          {taskStatuses.map(status => (
            <div key={status.id} className="kanban-column" data-status={status.id}>
              <div className="column-header">
                <span className="status-icon">{status.icon}</span>
                <h2>{status.name}</h2>
                <span className="task-count">{getStatusTasks(status.id).length}</span>
              </div>
              
              <div className="task-cards">
                {getStatusTasks(status.id).map(task => (
                  <div key={task.id} className="task-card">
                    <div className="task-card-header">
                      <span 
                        className="task-priority"
                        style={{ background: taskPriorities.find(p => p.id === task.priority)?.color }}
                      >
                        {taskPriorities.find(p => p.id === task.priority)?.icon}
                      </span>
                      <h3>{task.title}</h3>
                    </div>
                    
                    {task.description && (
                      <p className="task-description">{task.description}</p>
                    )}
                    
                    <div className="task-meta">
                      {task.dueDate && (
                        <span className="task-due-date">
                          📅 {new Date(task.dueDate).toLocaleDateString()}
                        </span>
                      )}
                      
                      {task.assignedTo ? (
                        <span className="task-assignee">
                          {getAgentAvatar(task.assignedTo)} {getAgentName(task.assignedTo)}
                        </span>
                      ) : (
                        <span className="task-assignee unassigned">
                          👤 No asignado
                        </span>
                      )}
                    </div>
                    
                    <div className="task-actions">
                      {canEditTask(task) && (
                        <>
                          <button 
                            className="action-btn edit"
                            onClick={() => handleEditTask(task)}
                          >
                            ✏️
                          </button>
                          
                          {currentAgent.role === 'coordinator' && (
                            <button 
                              className="action-btn delete"
                              onClick={() => handleDeleteTask(task.id)}
                            >
                              🗑️
                            </button>
                          )}
                        </>
                      )}
                      
                      {currentAgent.role === 'coordinator' && (
                        <div className="assign-dropdown">
                          <button className="action-btn assign">👥</button>
                          <div className="assign-options">
                            {agents.map(agent => (
                              <div 
                                key={agent.id} 
                                className="assign-option"
                                onClick={() => handleAssignTask(task.id, agent.id)}
                              >
                                {agent.avatar} {agent.name}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {task.status !== 'completed' && (
                        <button 
                          className="action-btn next-status"
                          onClick={() => {
                            const currentIndex = taskStatuses.findIndex(s => s.id === task.status);
                            const nextStatus = taskStatuses[currentIndex + 1]?.id || 'completed';
                            handleUpdateTaskStatus(task.id, nextStatus);
                          }}
                        >
                          ➡️
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="task-list-view">
          <table className="task-table">
            <thead>
              <tr>
                <th>Título</th>
                <th>Descripción</th>
                <th>Estado</th>
                <th>Prioridad</th>
                <th>Asignado a</th>
                <th>Fecha Límite</th>
                <th>Acciones</th>
              </tr>
            </thead>
            <tbody>
              {getFilteredTasks().map(task => (
                <tr key={task.id}>
                  <td>{task.title}</td>
                  <td>{task.description || '-'}</td>
                  <td>
                    <span className="status-pill" data-status={task.status}>
                      {taskStatuses.find(s => s.id === task.status)?.name}
                    </span>
                  </td>
                  <td>
                    <span 
                      className="priority-pill"
                      style={{ background: taskPriorities.find(p => p.id === task.priority)?.color }}
                    >
                      {taskPriorities.find(p => p.id === task.priority)?.name}
                    </span>
                  </td>
                  <td>
                    {task.assignedTo ? (
                      <span className="assignee">
                        {getAgentAvatar(task.assignedTo)} {getAgentName(task.assignedTo)}
                      </span>
                    ) : (
                      <span className="assignee unassigned">No asignado</span>
                    )}
                  </td>
                  <td>{task.dueDate ? new Date(task.dueDate).toLocaleDateString() : '-'}</td>
                  <td>
                    <div className="table-actions">
                      {canEditTask(task) && (
                        <button 
                          className="table-action edit"
                          onClick={() => handleEditTask(task)}
                        >
                          ✏️ Editar
                        </button>
                      )}
                      
                      {currentAgent.role === 'coordinator' && (
                        <>
                          <button 
                            className="table-action delete"
                            onClick={() => handleDeleteTask(task.id)}
                          >
                            🗑️ Eliminar
                          </button>
                          <div className="assign-dropdown inline">
                            <button className="table-action assign">👥 Asignar</button>
                            <div className="assign-options">
                              {agents.map(agent => (
                                <div 
                                  key={agent.id} 
                                  className="assign-option"
                                  onClick={() => handleAssignTask(task.id, agent.id)}
                                >
                                  {agent.avatar} {agent.name}
                                </div>
                              ))}
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {(isCreatingTask || editingTask) && (
        <div className="modal-backdrop">
          <div className="modal">
            <div className="modal-header">
              <h2>{editingTask ? 'Editar Tarea' : 'Crear Nueva Tarea'}</h2>
              <button 
                className="close-btn"
                onClick={() => {
                  setIsCreatingTask(false);
                  setEditingTask(null);
                  setErrors({});
                }}
              >
                ×
              </button>
            </div>
            
            <form className="task-form" onSubmit={editingTask ? handleSaveTaskChanges : handleCreateTask}>
              <div className="form-group">
                <label htmlFor="title">Título de la Tarea</label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={newTask.title}
                  onChange={handleInputChange}
                  placeholder="Ej: Implementar API de usuarios"
                  className={errors.title ? 'error' : ''}
                />
                {errors.title && <span className="error-message">{errors.title}</span>}
              </div>
              
              <div className="form-group">
                <label htmlFor="description">Descripción</label>
                <textarea
                  id="description"
                  name="description"
                  value={newTask.description}
                  onChange={handleInputChange}
                  placeholder="Detalles de la tarea..."
                  rows="3"
                ></textarea>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="status">Estado</label>
                  <select
                    id="status"
                    name="status"
                    value={newTask.status}
                    onChange={handleInputChange}
                  >
                    {taskStatuses.map(status => (
                      <option key={status.id} value={status.id}>{status.name}</option>
                    ))}
                  </select>
                </div>
                
                <div className="form-group">
                  <label htmlFor="priority">Prioridad</label>
                  <select
                    id="priority"
                    name="priority"
                    value={newTask.priority}
                    onChange={handleInputChange}
                  >
                    {taskPriorities.map(priority => (
                      <option key={priority.id} value={priority.id}>{priority.name}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="assignedTo">Asignar a</label>
                  <select
                    id="assignedTo"
                    name="assignedTo"
                    value={newTask.assignedTo}
                    onChange={handleInputChange}
                  >
                    <option value="">No asignado</option>
                    {agents.map(agent => (
                      <option key={agent.id} value={agent.id}>
                        {agent.name} ({agent.role})
                      </option>
                    ))}
                  </select>
                </div>
                
                <div className="form-group">
                  <label htmlFor="dueDate">Fecha Límite</label>
                  <input
                    type="date"
                    id="dueDate"
                    name="dueDate"
                    value={newTask.dueDate}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              
              <div className="form-actions">
                <button 
                  type="button" 
                  className="cancel-btn"
                  onClick={() => {
                    setIsCreatingTask(false);
                    setEditingTask(null);
                    setErrors({});
                  }}
                >
                  Cancelar
                </button>
                <button type="submit" className="save-btn">
                  {editingTask ? 'Guardar Cambios' : 'Crear Tarea'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      <style jsx>{`
        .task-board {
          background: white;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.08);
          padding: 1.5rem;
        }

        .task-board-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .task-board-header h1 {
          margin: 0;
          color: #333;
          font-size: 1.8rem;
        }

        .task-board-controls {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .view-toggle {
          display: flex;
          border-radius: 25px;
          overflow: hidden;
          border: 1px solid #ddd;
        }

        .view-btn {
          padding: 0.5rem 1rem;
          background: white;
          border: none;
          cursor: pointer;
          transition: background-color 0.3s;
        }

        .view-btn:hover {
          background: #f8f9fa;
        }

        .view-btn.active {
          background: #667eea;
          color: white;
        }

        .create-task-btn {
          padding: 0.5rem 1rem;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 25px;
          cursor: pointer;
          transition: all 0.3s;
          font-weight: 500;
        }

        .create-task-btn:hover {
          background: #5469d4;
          transform: translateY(-2px);
        }

        .task-filters {
          display: flex;
          gap: 1.5rem;
          margin-bottom: 1.5rem;
          flex-wrap: wrap;
        }

        .filter-group {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .filter-group label {
          color: #666;
          font-size: 0.9rem;
          white-space: nowrap;
        }

        .filter-group select {
          padding: 0.5rem;
          border: 1px solid #ddd;
          border-radius: 8px;
          font-size: 0.9rem;
          background: white;
          min-width: 150px;
        }

        .kanban-board {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1.5rem;
          margin-bottom: 1rem;
        }

        .kanban-column {
          background: #f8f9fa;
          border-radius: 10px;
          padding: 1rem;
        }

        .column-header {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 1px solid #eee;
        }

        .status-icon {
          font-size: 1.2rem;
        }

        .column-header h2 {
          margin: 0;
          font-size: 1.1rem;
          color: #333;
          flex: 1;
        }

        .task-count {
          background: #e9ecef;
          color: #666;
          font-size: 0.8rem;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
          font-weight: 600;
        }

        .task-cards {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          min-height: 100px;
        }

        .task-card {
          background: white;
          border-radius: 10px;
          padding: 1rem;
          box-shadow: 0 2px 5px rgba(0,0,0,0.05);
          transition: all 0.3s;
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .task-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .task-card-header {
          display: flex;
          align-items: flex-start;
          gap: 0.5rem;
        }

        .task-priority {
          padding: 0.25rem;
          border-radius: 5px;
          color: white;
          font-size: 0.8rem;
          display: flex;
          align-items: center;
          gap: 0.25rem;
          flex-shrink: 0;
        }

        .task-card-header h3 {
          margin: 0;
          font-size: 1rem;
          color: #333;
          flex: 1;
        }

        .task-description {
          color: #666;
          font-size: 0.9rem;
          margin: 0;
        }

        .task-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 0.5rem;
          font-size: 0.8rem;
          color: #999;
        }

        .task-due-date {
          background: #f8f9fa;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
        }

        .task-assignee {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          background: #f8f9fa;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
        }

        .task-assignee.unassigned {
          color: #999;
          font-style: italic;
        }

        .task-actions {
          display: flex;
          gap: 0.5rem;
          justify-content: flex-end;
          margin-top: 0.5rem;
        }

        .action-btn {
          background: #f8f9fa;
          border: none;
          border-radius: 5px;
          padding: 0.35rem 0.75rem;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .action-btn:hover {
          background: #e9ecef;
        }

        .action-btn.edit {
          color: #3498db;
        }

        .action-btn.delete {
          color: #e74c3c;
        }

        .action-btn.assign {
          color: #9b59b6;
        }

        .action-btn.next-status {
          color: #27ae60;
        }

        .assign-dropdown {
          position: relative;
        }

        .assign-dropdown:hover .assign-options {
          display: block;
        }

        .assign-options {
          display: none;
          position: absolute;
          right: 0;
          bottom: 100%;
          background: white;
          border-radius: 8px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.15);
          min-width: 150px;
          max-height: 200px;
          overflow-y: auto;
          z-index: 100;
        }

        .assign-option {
          padding: 0.5rem 0.75rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          transition: background-color 0.3s;
        }

        .assign-option:hover {
          background: #f8f9fa;
        }

        .task-list-view {
          overflow-x: auto;
        }

        .task-table {
          width: 100%;
          border-collapse: collapse;
          background: white;
          border-radius: 10px;
          overflow: hidden;
        }

        .task-table th {
          background: #f8f9fa;
          padding: 0.75rem 1rem;
          text-align: left;
          color: #333;
          font-weight: 600;
          border-bottom: 2px solid #eee;
        }

        .task-table td {
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #eee;
        }

        .task-table tr:hover {
          background: #f8f9fa;
        }

        .status-pill {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
          font-size: 0.8rem;
          color: white;
        }

        .status-pill[data-status="pending"] {
          background: #f39c12;
        }

        .status-pill[data-status="in-progress"] {
          background: #3498db;
        }

        .status-pill[data-status="review"] {
          background: #9b59b6;
        }

        .status-pill[data-status="completed"] {
          background: #27ae60;
        }

        .priority-pill {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
          font-size: 0.8rem;
          color: white;
        }

        .assignee {
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .assignee.unassigned {
          color: #999;
          font-style: italic;
        }

        .table-actions {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
        }

        .table-action {
          background: #f8f9fa;
          border: none;
          border-radius: 5px;
          padding: 0.35rem 0.75rem;
          cursor: pointer;
          transition: all 0.3s;
          font-size: 0.9rem;
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .table-action:hover {
          background: #e9ecef;
        }

        .table-action.edit {
          color: #3498db;
        }

        .table-action.delete {
          color: #e74c3c;
        }

        .table-action.assign {
          color: #9b59b6;
        }

        .assign-dropdown.inline {
          display: inline-block;
          position: relative;
        }

        .assign-dropdown.inline:hover .assign-options {
          display: block;
        }

        .modal-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0,0,0,0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 15px;
          max-width: 600px;
          width: 90%;
          max-height: 80vh;
          overflow-y: auto;
          box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
          padding: 1.5rem 1.5rem 0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #eee;
          margin-bottom: 1.5rem;
        }

        .modal-header h2 {
          margin: 0;
          color: #333;
          font-size: 1.5rem;
        }

        .close-btn {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #999;
          transition: color 0.3s;
        }

        .close-btn:hover {
          color: #666;
        }

        .task-form {
          padding: 0 1.5rem 1.5rem;
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-group label {
          color: #333;
          font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
          padding: 0.75rem;
          border: 2px solid #ddd;
          border-radius: 8px;
          font-size: 1rem;
          transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: #667eea;
        }

        .form-group input.error {
          border-color: #e74c3c;
        }

        .error-message {
          color: #e74c3c;
          font-size: 0.85rem;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1.25rem;
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
          margin-top: 1rem;
        }

        .cancel-btn {
          padding: 0.75rem 1.5rem;
          background: #f8f9fa;
          border: none;
          border-radius: 10px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s;
        }

        .cancel-btn:hover {
          background: #e9ecef;
        }

        .save-btn {
          padding: 0.75rem 1.5rem;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 10px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s;
        }

        .save-btn:hover {
          background: #5469d4;
        }

        @media (max-width: 768px) {
          .kanban-board {
            grid-template-columns: 1fr;
          }

          .form-row {
            grid-template-columns: 1fr;
          }

          .task-board-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .task-board-controls {
            width: 100%;
            justify-content: space-between;
          }

          .task-filters {
            flex-direction: column;
            gap: 0.75rem;
          }
        }
      `}</style>
    </div>
  );
}
