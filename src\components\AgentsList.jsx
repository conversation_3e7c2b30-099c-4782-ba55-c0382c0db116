import { useApp } from '../context/AppContext';

const roleInfo = {
  coordinator: { name: '<PERSON><PERSON><PERSON><PERSON>', color: '#e74c3c', icon: '👑' },
  developer: { name: '<PERSON>arrollador', color: '#3498db', icon: '💻' },
  tester: { name: 'Tester', color: '#f39c12', icon: '🧪' },
  analyst: { name: '<PERSON><PERSON><PERSON>', color: '#9b59b6', icon: '📊' },
  designer: { name: '<PERSON><PERSON><PERSON><PERSON>', color: '#e67e22', icon: '🎨' },
  manager: { name: 'Manager', color: '#27ae60', icon: '📋' }
};

export default function AgentsList({ expanded = false }) {
  const { state } = useApp();
  const { agents, tasks, currentAgent } = state;
  
  const getAgentTasks = (agentId) => {
    return tasks.filter(task => task.assignedTo === agentId);
  };
  
  const getAgentStats = (agentId) => {
    const agentTasks = getAgentTasks(agentId);
    return {
      total: agentTasks.length,
      pending: agentTasks.filter(t => t.status === 'pending').length,
      inProgress: agentTasks.filter(t => t.status === 'in-progress').length,
      completed: agentTasks.filter(t => t.status === 'completed').length
    };
  };
  
  const formatLastSeen = (lastSeen) => {
    if (!lastSeen) return 'Nunca';
    const now = new Date();
    const diff = now - new Date(lastSeen);
    
    if (diff < 60000) return 'Ahora';
    if (diff < 3600000) return `hace ${Math.floor(diff / 60000)} min`;
    if (diff < 86400000) return `hace ${Math.floor(diff / 3600000)} horas`;
    return new Date(lastSeen).toLocaleDateString();
  };
  
  return (
    <div className={`agents-list ${expanded ? 'expanded' : ''}`}>
      {!expanded && (
        <div className="agents-header">
          <h2>👥 Agentes Conectados</h2>
          <span className="online-count">{agents.length} online</span>
        </div>
      )}
      
      {expanded ? (
        <div className="agents-expanded-view">
          <div className="agents-header-expanded">
            <h1>👥 Directorio de Agentes</h1>
            <p>Lista completa de agentes en el sistema</p>
          </div>
          
          <div className="agents-grid">
            {agents.length === 0 ? (
              <div className="no-agents-full">
                <span>👻</span>
                <p>No hay agentes conectados en este momento.</p>
              </div>
            ) : (
              agents.map(agent => {
                const stats = getAgentStats(agent.id);
                const isCurrent = agent.id === currentAgent.id;
                
                return (
                  <div key={agent.id} className="agent-profile-card">
                    <div className="agent-profile-header">
                      <div className="agent-profile-avatar">
                        {agent.avatar}
                        <span 
                          className="agent-status-indicator"
                          style={{ 
                            background: agent.status === 'online' ? '#27ae60' : '#999'
                          }}
                        ></span>
                      </div>
                      <div className="agent-profile-info">
                        <h3>{agent.name} {isCurrent && <span>(Tú)</span>}</h3>
                        <div className="agent-role-badge" style={{ color: roleInfo[agent.role]?.color }}>
                          {roleInfo[agent.role]?.icon} {roleInfo[agent.role]?.name}
                        </div>
                        <p className="agent-email">{agent.email}</p>
                        <p className="agent-last-seen">
                          {agent.status === 'online' ? 'En línea ahora' : formatLastSeen(agent.lastSeen)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="agent-stats-grid">
                      <div className="stat-box">
                        <span className="stat-value">{stats.total}</span>
                        <span className="stat-label">Tareas</span>
                      </div>
                      <div className="stat-box pending">
                        <span className="stat-value">{stats.pending}</span>
                        <span className="stat-label">Pendientes</span>
                      </div>
                      <div className="stat-box progress">
                        <span className="stat-value">{stats.inProgress}</span>
                        <span className="stat-label">En Progreso</span>
                      </div>
                      <div className="stat-box completed">
                        <span className="stat-value">{stats.completed}</span>
                        <span className="stat-label">Completadas</span>
                      </div>
                    </div>
                    
                    {stats.total > 0 && (
                      <div className="agent-tasks-preview">
                        <h4>Tareas Asignadas</h4>
                        <div className="task-preview-list">
                          {getAgentTasks(agent.id).slice(0, 3).map(task => (
                            <div key={task.id} className="task-preview-item">
                              <span className="task-status-dot" data-status={task.status}></span>
                              <span className="task-title">{task.title}</span>
                            </div>
                          ))}
                        </div>
                        {stats.total > 3 && (
                          <p className="more-tasks">+ {stats.total - 3} tareas más</p>
                        )}
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>
      ) : (
        <div className="agents-compact-view">
          {agents.length === 0 ? (
            <div className="no-agents">
              <p>No hay agentes conectados.</p>
            </div>
          ) : (
            agents.map(agent => {
              const isCurrent = agent.id === currentAgent.id;
              return (
                <div key={agent.id} className="agent-item">
                  <div className="agent-avatar">
                    {agent.avatar}
                    <span 
                      className="status-indicator"
                      style={{ 
                        background: agent.status === 'online' ? '#27ae60' : '#999'
                      }}
                    ></span>
                  </div>
                  <div className="agent-details">
                    <h3>{agent.name} {isCurrent && <span>(Tú)</span>}</h3>
                    <span 
                      className="agent-role"
                      style={{ color: roleInfo[agent.role]?.color }}
                    >
                      {roleInfo[agent.role]?.name}
                    </span>
                  </div>
                </div>
              );
            })
          )}
        </div>
      )}
      
      <style jsx>{`
        .agents-list {
          background: white;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.08);
          padding: 1.5rem;
        }

        .agents-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.25rem;
          padding-bottom: 0.75rem;
          border-bottom: 1px solid #eee;
        }

        .agents-header h2 {
          margin: 0;
          color: #333;
          font-size: 1.2rem;
        }

        .online-count {
          background: #27ae60;
          color: white;
          font-size: 0.8rem;
          padding: 0.25rem 0.5rem;
          border-radius: 15px;
        }

        .agents-compact-view {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          max-height: 300px;
          overflow-y: auto;
        }

        .no-agents {
          color: #666;
          text-align: center;
          padding: 1.5rem 0;
        }

        .no-agents p {
          margin: 0;
          font-size: 0.9rem;
        }

        .agent-item {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.5rem;
          border-radius: 10px;
          transition: background-color 0.3s;
        }

        .agent-item:hover {
          background: #f8f9fa;
        }

        .agent-avatar {
          position: relative;
          font-size: 2rem;
          flex-shrink: 0;
        }

        .status-indicator {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          border: 2px solid white;
          box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
        }

        .agent-details {
          flex: 1;
          overflow: hidden;
        }

        .agent-details h3 {
          margin: 0 0 0.25rem 0;
          font-size: 0.95rem;
          color: #333;
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .agent-details h3 span {
          font-size: 0.8rem;
          color: #666;
          font-weight: normal;
        }

        .agent-role {
          font-size: 0.8rem;
          font-weight: 500;
          display: inline-block;
        }

        /* Expanded View Styles */
        .agents-list.expanded {
          min-height: 70vh;
        }

        .agents-header-expanded {
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #eee;
        }

        .agents-header-expanded h1 {
          margin: 0 0 0.5rem 0;
          color: #333;
          font-size: 1.8rem;
        }

        .agents-header-expanded p {
          margin: 0;
          color: #666;
          font-size: 1rem;
        }

        .agents-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1.5rem;
        }

        .no-agents-full {
          grid-column: 1 / -1;
          text-align: center;
          padding: 3rem;
          color: #666;
        }

        .no-agents-full span {
          font-size: 3rem;
          display: block;
          margin-bottom: 1rem;
        }

        .no-agents-full p {
          margin: 0;
          font-size: 1rem;
        }

        .agent-profile-card {
          background: white;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
          padding: 1.5rem;
          transition: transform 0.3s;
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
        }

        .agent-profile-card:hover {
          transform: translateY(-5px);
        }

        .agent-profile-header {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
        }

        .agent-profile-avatar {
          position: relative;
          font-size: 3.5rem;
          flex-shrink: 0;
        }

        .agent-status-indicator {
          position: absolute;
          bottom: 5px;
          right: 5px;
          width: 14px;
          height: 14px;
          border-radius: 50%;
          border: 3px solid white;
          box-shadow: 0 0 0 1px rgba(0,0,0,0.1);
        }

        .agent-profile-info {
          flex: 1;
          overflow: hidden;
        }

        .agent-profile-info h3 {
          margin: 0 0 0.25rem 0;
          font-size: 1.2rem;
          color: #333;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .agent-profile-info h3 span {
          font-size: 0.9rem;
          color: #666;
          font-weight: normal;
        }

        .agent-role-badge {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .agent-email {
          color: #666;
          font-size: 0.9rem;
          margin-bottom: 0.25rem;
          word-break: break-all;
        }

        .agent-last-seen {
          color: #999;
          font-size: 0.85rem;
          font-style: italic;
        }

        .agent-stats-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 0.75rem;
        }

        .stat-box {
          background: #f8f9fa;
          border-radius: 10px;
          padding: 0.75rem;
          text-align: center;
        }

        .stat-box.pending {
          border-left: 3px solid #f39c12;
        }

        .stat-box.progress {
          border-left: 3px solid #3498db;
        }

        .stat-box.completed {
          border-left: 3px solid #27ae60;
        }

        .stat-value {
          display: block;
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
          margin-bottom: 0.25rem;
        }

        .stat-label {
          display: block;
          font-size: 0.8rem;
          color: #666;
        }

        .agent-tasks-preview {
          border-top: 1px solid #eee;
          padding-top: 1rem;
        }

        .agent-tasks-preview h4 {
          margin: 0 0 0.75rem 0;
          font-size: 0.9rem;
          color: #333;
        }

        .task-preview-list {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .task-preview-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background: #f8f9fa;
          padding: 0.5rem 0.75rem;
          border-radius: 8px;
        }

        .task-status-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .task-status-dot[data-status="pending"] {
          background: #f39c12;
        }

        .task-status-dot[data-status="in-progress"] {
          background: #3498db;
        }

        .task-status-dot[data-status="review"] {
          background: #9b59b6;
        }

        .task-status-dot[data-status="completed"] {
          background: #27ae60;
        }

        .task-title {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 0.9rem;
          color: #333;
        }

        .more-tasks {
          margin-top: 0.5rem;
          font-size: 0.8rem;
          color: #666;
          text-align: center;
        }

        @media (max-width: 768px) {
          .agents-grid {
            grid-template-columns: 1fr;
          }

          .agent-stats-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      `}</style>
    </div>
  );
}
