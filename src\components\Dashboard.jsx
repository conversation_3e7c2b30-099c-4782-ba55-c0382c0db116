import { useApp } from '../context/AppContext';
import TaskBoard from './TaskBoard';
import ChatPanel from './ChatPanel';
import AgentsList from './AgentsList';
import Notifications from './Notifications';

export default function Dashboard() {
  const { state, actions } = useApp();
  const { currentAgent, activeView, agents, tasks, notifications } = state;

  const handleViewChange = (view) => {
    actions.setActiveView(view);
  };

  const handleLogout = () => {
    actions.logoutAgent();
  };

  const getTaskStats = () => {
    const userTasks = tasks.filter(task => 
      task.assignedTo === currentAgent.id || task.createdBy === currentAgent.id
    );
    
    return {
      total: userTasks.length,
      pending: userTasks.filter(t => t.status === 'pending').length,
      inProgress: userTasks.filter(t => t.status === 'in-progress').length,
      completed: userTasks.filter(t => t.status === 'completed').length
    };
  };

  const stats = getTaskStats();

  return (
    <div className="dashboard">
      {/* Header */}
      <header className="dashboard-header">
        <div className="header-left">
          <h1>🚀 Sistema de Agentes</h1>
          <div className="agent-info">
            <span className="agent-avatar">{currentAgent.avatar}</span>
            <div className="agent-details">
              <span className="agent-name">{currentAgent.name}</span>
              <span className="agent-role">{currentAgent.role}</span>
            </div>
          </div>
        </div>
        
        <div className="header-center">
          <nav className="nav-tabs">
            <button 
              className={`nav-tab ${activeView === 'dashboard' ? 'active' : ''}`}
              onClick={() => handleViewChange('dashboard')}
            >
              📊 Dashboard
            </button>
            <button 
              className={`nav-tab ${activeView === 'tasks' ? 'active' : ''}`}
              onClick={() => handleViewChange('tasks')}
            >
              ✅ Tareas
            </button>
            <button 
              className={`nav-tab ${activeView === 'chat' ? 'active' : ''}`}
              onClick={() => handleViewChange('chat')}
            >
              💬 Chat
            </button>
            <button 
              className={`nav-tab ${activeView === 'agents' ? 'active' : ''}`}
              onClick={() => handleViewChange('agents')}
            >
              👥 Agentes
            </button>
          </nav>
        </div>

        <div className="header-right">
          <Notifications />
          <button className="logout-btn" onClick={handleLogout}>
            🚪 Salir
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        {activeView === 'dashboard' && (
          <div className="dashboard-overview">
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">📋</div>
                <div className="stat-info">
                  <h3>{stats.total}</h3>
                  <p>Tareas Totales</p>
                </div>
              </div>
              
              <div className="stat-card pending">
                <div className="stat-icon">⏳</div>
                <div className="stat-info">
                  <h3>{stats.pending}</h3>
                  <p>Pendientes</p>
                </div>
              </div>
              
              <div className="stat-card progress">
                <div className="stat-icon">🔄</div>
                <div className="stat-info">
                  <h3>{stats.inProgress}</h3>
                  <p>En Progreso</p>
                </div>
              </div>
              
              <div className="stat-card completed">
                <div className="stat-icon">✅</div>
                <div className="stat-info">
                  <h3>{stats.completed}</h3>
                  <p>Completadas</p>
                </div>
              </div>
            </div>

            <div className="dashboard-content">
              <div className="dashboard-left">
                <div className="recent-tasks">
                  <h2>📝 Tareas Recientes</h2>
                  <div className="task-list">
                    {tasks.slice(0, 5).map(task => (
                      <div key={task.id} className="task-item">
                        <div className="task-status" data-status={task.status}></div>
                        <div className="task-info">
                          <h4>{task.title}</h4>
                          <p>{task.description}</p>
                          <span className="task-meta">
                            {task.priority} • {new Date(task.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="dashboard-right">
                <AgentsList />
              </div>
            </div>
          </div>
        )}

        {activeView === 'tasks' && <TaskBoard />}
        {activeView === 'chat' && <ChatPanel />}
        {activeView === 'agents' && <AgentsList expanded={true} />}
      </main>

      <style jsx>{`
        .dashboard {
          min-height: 100vh;
          background: #f5f7fa;
        }

        .dashboard-header {
          background: white;
          padding: 1rem 2rem;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .header-left {
          display: flex;
          align-items: center;
          gap: 2rem;
        }

        .header-left h1 {
          margin: 0;
          color: #333;
          font-size: 1.5rem;
        }

        .agent-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background: #f8f9fa;
          border-radius: 25px;
        }

        .agent-avatar {
          font-size: 1.5rem;
        }

        .agent-details {
          display: flex;
          flex-direction: column;
        }

        .agent-name {
          font-weight: 600;
          color: #333;
        }

        .agent-role {
          font-size: 0.8rem;
          color: #666;
          text-transform: capitalize;
        }

        .nav-tabs {
          display: flex;
          gap: 0.5rem;
        }

        .nav-tab {
          padding: 0.75rem 1.5rem;
          border: none;
          background: transparent;
          border-radius: 25px;
          cursor: pointer;
          transition: all 0.3s;
          font-weight: 500;
        }

        .nav-tab:hover {
          background: #f8f9fa;
        }

        .nav-tab.active {
          background: #667eea;
          color: white;
        }

        .header-right {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .logout-btn {
          padding: 0.5rem 1rem;
          border: 1px solid #ddd;
          background: white;
          border-radius: 20px;
          cursor: pointer;
          transition: all 0.3s;
        }

        .logout-btn:hover {
          background: #f8f9fa;
        }

        .dashboard-main {
          padding: 2rem;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        .stat-card {
          background: white;
          padding: 1.5rem;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.08);
          display: flex;
          align-items: center;
          gap: 1rem;
          transition: transform 0.3s;
        }

        .stat-card:hover {
          transform: translateY(-5px);
        }

        .stat-card.pending {
          border-left: 4px solid #f39c12;
        }

        .stat-card.progress {
          border-left: 4px solid #3498db;
        }

        .stat-card.completed {
          border-left: 4px solid #27ae60;
        }

        .stat-icon {
          font-size: 2rem;
          opacity: 0.8;
        }

        .stat-info h3 {
          margin: 0;
          font-size: 2rem;
          color: #333;
        }

        .stat-info p {
          margin: 0;
          color: #666;
          font-size: 0.9rem;
        }

        .dashboard-content {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 2rem;
        }

        .recent-tasks {
          background: white;
          padding: 1.5rem;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .recent-tasks h2 {
          margin: 0 0 1rem 0;
          color: #333;
        }

        .task-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .task-item {
          display: flex;
          align-items: center;
          gap: 1rem;
          padding: 1rem;
          background: #f8f9fa;
          border-radius: 10px;
          transition: all 0.3s;
        }

        .task-item:hover {
          background: #e9ecef;
        }

        .task-status {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        .task-status[data-status="pending"] {
          background: #f39c12;
        }

        .task-status[data-status="in-progress"] {
          background: #3498db;
        }

        .task-status[data-status="completed"] {
          background: #27ae60;
        }

        .task-info h4 {
          margin: 0 0 0.25rem 0;
          color: #333;
        }

        .task-info p {
          margin: 0 0 0.5rem 0;
          color: #666;
          font-size: 0.9rem;
        }

        .task-meta {
          font-size: 0.8rem;
          color: #999;
        }

        @media (max-width: 768px) {
          .dashboard-header {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
          }

          .header-left, .header-center, .header-right {
            width: 100%;
            justify-content: center;
          }

          .nav-tabs {
            justify-content: center;
            flex-wrap: wrap;
          }

          .dashboard-content {
            grid-template-columns: 1fr;
          }

          .stats-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      `}</style>
    </div>
  );
}
