import Head from 'next/head';
import { AppProvider, useApp } from '../context/AppContext';
import AgentLogin from '../components/AgentLogin';
import Dashboard from '../components/Dashboard';

export default function Home() {
  return (
    <AppProvider>
      <AppContent />
    </AppProvider>
  );
}

function AppContent() {
  const { state } = useApp();
  const { isAuthenticated } = state;

  return (
    <>
      <Head>
        <title>Sistema de Agentes y Tareas</title>
        <meta name="description" content="Sistema colaborativo para gestión de tareas y agentes" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      <main>
        {isAuthenticated ? <Dashboard /> : <AgentLogin />}
      </main>
    </>
  );
}
