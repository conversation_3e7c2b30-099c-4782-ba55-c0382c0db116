import { useState, useEffect, useRef } from 'react';
import { useApp } from '../context/AppContext';

export default function ChatPanel() {
  const { state, actions } = useApp();
  const { messages, currentAgent, agents } = state;
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef(null);
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!messageText.trim()) {
      return;
    }
    
    actions.sendMessage({
      content: messageText.trim()
    });
    
    setMessageText('');
  };
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  const getAgentName = (agentId) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.name : 'Desconocido';
  };
  
  const getAgentAvatar = (agentId) => {
    const agent = agents.find(a => a.id === agentId);
    return agent ? agent.avatar : '👤';
  };
  
  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };
  
  return (
    <div className="chat-panel">
      <div className="chat-header">
        <h1>💬 Chat en Tiempo Real</h1>
        <p>Conversa con otros agentes y coordina tareas</p>
      </div>
      
      <div className="chat-messages">
        {messages.length === 0 ? (
          <div className="no-messages">
            <span>📭</span>
            <p>No hay mensajes aún. ¡Sé el primero en escribir!</p>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwnMessage = message.agentId === currentAgent.id;
            return (
              <div 
                key={index} 
                className={`message ${isOwnMessage ? 'own-message' : ''}`}
              >
                <div className="message-avatar">
                  {getAgentAvatar(message.agentId)}
                </div>
                <div className="message-content">
                  <div className="message-header">
                    <span className="message-sender">
                      {isOwnMessage ? 'Tú' : getAgentName(message.agentId)}
                    </span>
                    <span className="message-time">
                      {formatTime(message.timestamp)}
                    </span>
                  </div>
                  <div className="message-text">
                    {message.content}
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>
      
      <form className="chat-input" onSubmit={handleSendMessage}>
        <input
          type="text"
          placeholder="Escribe un mensaje..."
          value={messageText}
          onChange={e => setMessageText(e.target.value)}
        />
        <button type="submit" className="send-btn">
          ✈️ Enviar
        </button>
      </form>
      
      <style jsx>{`
        .chat-panel {
          background: white;
          border-radius: 15px;
          box-shadow: 0 5px 15px rgba(0,0,0,0.08);
          padding: 1.5rem;
          display: flex;
          flex-direction: column;
          height: 70vh;
        }

        .chat-header {
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #eee;
        }

        .chat-header h1 {
          margin: 0 0 0.5rem 0;
          color: #333;
          font-size: 1.8rem;
        }

        .chat-header p {
          margin: 0;
          color: #666;
          font-size: 1rem;
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 0.5rem;
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .no-messages {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: #666;
        }

        .no-messages span {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .no-messages p {
          margin: 0;
          font-size: 1rem;
        }

        .message {
          display: flex;
          gap: 0.75rem;
          max-width: 80%;
        }

        .message.own-message {
          align-self: flex-end;
          flex-direction: row-reverse;
        }

        .message-avatar {
          font-size: 2rem;
          flex-shrink: 0;
        }

        .message-content {
          background: #f8f9fa;
          border-radius: 10px;
          padding: 0.75rem 1rem;
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
          max-width: 100%;
          word-break: break-word;
        }

        .own-message .message-content {
          background: #667eea;
          color: white;
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 1rem;
        }

        .message-sender {
          font-weight: 600;
          font-size: 0.9rem;
        }

        .message-time {
          font-size: 0.8rem;
          opacity: 0.8;
          white-space: nowrap;
        }

        .message-text {
          font-size: 0.95rem;
          line-height: 1.4;
        }

        .chat-input {
          margin-top: 1.5rem;
          padding-top: 1rem;
          border-top: 1px solid #eee;
          display: flex;
          gap: 1rem;
        }

        .chat-input input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 2px solid #ddd;
          border-radius: 25px;
          font-size: 1rem;
          transition: border-color 0.3s;
        }

        .chat-input input:focus {
          outline: none;
          border-color: #667eea;
        }

        .send-btn {
          padding: 0 1.5rem;
          background: #667eea;
          color: white;
          border: none;
          border-radius: 25px;
          cursor: pointer;
          font-weight: 500;
          transition: all 0.3s;
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .send-btn:hover {
          background: #5469d4;
          transform: translateY(-2px);
        }

        @media (max-width: 480px) {
          .chat-panel {
            height: 60vh;
            padding: 1rem;
          }

          .chat-input {
            flex-direction: column;
            gap: 0.75rem;
          }

          .send-btn {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
}
