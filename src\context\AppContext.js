import { createContext, useContext, useReducer, useEffect } from 'react';
import io from 'socket.io-client';

const AppContext = createContext();

// Estados iniciales
const initialState = {
  // Usuario/Agente actual
  currentAgent: null,
  isAuthenticated: false,
  
  // Agentes conectados
  agents: [],
  
  // Tareas
  tasks: [],
  
  // Chat
  messages: [],
  
  // Socket
  socket: null,
  
  // UI
  activeView: 'dashboard', // dashboard, tasks, chat, calendar
  notifications: []
};

// Tipos de acciones
const actionTypes = {
  SET_CURRENT_AGENT: 'SET_CURRENT_AGENT',
  SET_AUTHENTICATED: 'SET_AUTHENTICATED',
  UPDATE_AGENTS: 'UPDATE_AGENTS',
  ADD_AGENT: 'ADD_AGENT',
  REMOVE_AGENT: 'REMOVE_AGENT',
  SET_TASKS: 'SET_TASKS',
  ADD_TASK: 'ADD_TASK',
  UPDATE_TASK: 'UPDATE_TASK',
  DELETE_TASK: 'DELETE_TASK',
  ADD_MESSAGE: 'ADD_MESSAGE',
  SET_MESSAGES: 'SET_MESSAGES',
  SET_SOCKET: 'SET_SOCKET',
  SET_ACTIVE_VIEW: 'SET_ACTIVE_VIEW',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION'
};

// Reducer
function appReducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_CURRENT_AGENT:
      return { ...state, currentAgent: action.payload };
    
    case actionTypes.SET_AUTHENTICATED:
      return { ...state, isAuthenticated: action.payload };
    
    case actionTypes.UPDATE_AGENTS:
      return { ...state, agents: action.payload };
    
    case actionTypes.ADD_AGENT:
      return { ...state, agents: [...state.agents, action.payload] };
    
    case actionTypes.REMOVE_AGENT:
      return { 
        ...state, 
        agents: state.agents.filter(agent => agent.id !== action.payload) 
      };
    
    case actionTypes.SET_TASKS:
      return { ...state, tasks: action.payload };
    
    case actionTypes.ADD_TASK:
      return { ...state, tasks: [...state.tasks, action.payload] };
    
    case actionTypes.UPDATE_TASK:
      return {
        ...state,
        tasks: state.tasks.map(task => 
          task.id === action.payload.id ? { ...task, ...action.payload } : task
        )
      };
    
    case actionTypes.DELETE_TASK:
      return {
        ...state,
        tasks: state.tasks.filter(task => task.id !== action.payload)
      };
    
    case actionTypes.ADD_MESSAGE:
      return { ...state, messages: [...state.messages, action.payload] };
    
    case actionTypes.SET_MESSAGES:
      return { ...state, messages: action.payload };
    
    case actionTypes.SET_SOCKET:
      return { ...state, socket: action.payload };
    
    case actionTypes.SET_ACTIVE_VIEW:
      return { ...state, activeView: action.payload };
    
    case actionTypes.ADD_NOTIFICATION:
      return { 
        ...state, 
        notifications: [...state.notifications, { 
          id: Date.now(), 
          ...action.payload 
        }] 
      };
    
    case actionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(notif => notif.id !== action.payload)
      };
    
    default:
      return state;
  }
}

// Provider Component
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Inicializar Socket.IO
  useEffect(() => {
    const initSocket = async () => {
      await fetch('/api/socket');
      const socket = io();
      
      dispatch({ type: actionTypes.SET_SOCKET, payload: socket });

      // Listeners de Socket.IO
      socket.on('receive-message', (message) => {
        dispatch({ type: actionTypes.ADD_MESSAGE, payload: message });
      });

      socket.on('agent-connected', (agent) => {
        dispatch({ type: actionTypes.ADD_AGENT, payload: agent });
        dispatch({ 
          type: actionTypes.ADD_NOTIFICATION, 
          payload: { 
            type: 'info', 
            message: `${agent.name} se ha conectado`,
            timestamp: new Date()
          }
        });
      });

      socket.on('agent-disconnected', (agentId) => {
        dispatch({ type: actionTypes.REMOVE_AGENT, payload: agentId });
      });

      socket.on('task-created', (task) => {
        dispatch({ type: actionTypes.ADD_TASK, payload: task });
        dispatch({ 
          type: actionTypes.ADD_NOTIFICATION, 
          payload: { 
            type: 'success', 
            message: `Nueva tarea creada: ${task.title}`,
            timestamp: new Date()
          }
        });
      });

      socket.on('task-updated', (task) => {
        dispatch({ type: actionTypes.UPDATE_TASK, payload: task });
        dispatch({ 
          type: actionTypes.ADD_NOTIFICATION, 
          payload: { 
            type: 'info', 
            message: `Tarea actualizada: ${task.title}`,
            timestamp: new Date()
          }
        });
      });

      socket.on('agents-list', (agents) => {
        dispatch({ type: actionTypes.UPDATE_AGENTS, payload: agents });
      });

      socket.on('tasks-list', (tasks) => {
        dispatch({ type: actionTypes.SET_TASKS, payload: tasks });
      });

      return () => {
        socket.disconnect();
      };
    };

    initSocket();
  }, []);

  // Actions
  const actions = {
    // Autenticación
    loginAgent: (agent) => {
      dispatch({ type: actionTypes.SET_CURRENT_AGENT, payload: agent });
      dispatch({ type: actionTypes.SET_AUTHENTICATED, payload: true });
      
      if (state.socket) {
        state.socket.emit('agent-login', agent);
      }
    },

    logoutAgent: () => {
      if (state.socket && state.currentAgent) {
        state.socket.emit('agent-logout', state.currentAgent.id);
      }
      dispatch({ type: actionTypes.SET_CURRENT_AGENT, payload: null });
      dispatch({ type: actionTypes.SET_AUTHENTICATED, payload: false });
    },

    // Mensajes
    sendMessage: (message) => {
      if (state.socket && state.currentAgent) {
        const messageData = {
          ...message,
          agentId: state.currentAgent.id,
          agentName: state.currentAgent.name,
          timestamp: new Date()
        };
        state.socket.emit('send-message', messageData);
      }
    },

    // Tareas
    createTask: (task) => {
      if (state.socket && state.currentAgent) {
        const taskData = {
          ...task,
          id: Date.now().toString(),
          createdBy: state.currentAgent.id,
          createdAt: new Date(),
          status: 'pending'
        };
        state.socket.emit('create-task', taskData);
      }
    },

    updateTask: (taskId, updates) => {
      if (state.socket && state.currentAgent) {
        const taskData = {
          id: taskId,
          ...updates,
          updatedBy: state.currentAgent.id,
          updatedAt: new Date()
        };
        state.socket.emit('update-task', taskData);
      }
    },

    deleteTask: (taskId) => {
      if (state.socket && state.currentAgent) {
        state.socket.emit('delete-task', { 
          taskId, 
          deletedBy: state.currentAgent.id 
        });
      }
    },

    // UI
    setActiveView: (view) => {
      dispatch({ type: actionTypes.SET_ACTIVE_VIEW, payload: view });
    },

    removeNotification: (notificationId) => {
      dispatch({ type: actionTypes.REMOVE_NOTIFICATION, payload: notificationId });
    }
  };

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  );
}

// Hook personalizado
export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp debe ser usado dentro de AppProvider');
  }
  return context;
}

export { actionTypes };
