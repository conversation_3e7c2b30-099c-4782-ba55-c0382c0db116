import { Server } from "socket.io";

// Almacenamiento en memoria (en producción usar base de datos)
let agents = [];
let tasks = [];
let messages = [];

export default function SocketHandler(req, res) {
  if (res.socket.server.io) {
    console.log("Socket Already Setup");
    res.end();
    return;
  }

  const io = new Server(res.socket.server);
  res.socket.server.io = io;

  io.on("connection", (socket) => {
    console.log(`Cliente conectado: ${socket.id}`);

    // Enviar datos iniciales al cliente
    socket.emit("agents-list", agents);
    socket.emit("tasks-list", tasks);
    socket.emit("messages-list", messages);

    // === MANEJO DE MENSAJES ===
    socket.on("send-message", (messageData) => {
      const message = {
        ...messageData,
        id: Date.now().toString(),
        socketId: socket.id
      };
      messages.push(message);
      io.emit("receive-message", message);
    });

    // === MANEJO DE AGENTES ===
    socket.on("agent-login", (agentData) => {
      const agent = {
        ...agentData,
        id: agentData.id || socket.id,
        socketId: socket.id,
        status: 'online',
        lastSeen: new Date()
      };

      // Remover agente existente si ya está conectado
      agents = agents.filter(a => a.id !== agent.id);
      agents.push(agent);

      console.log(`Agente conectado: ${agent.name} (${agent.role})`);
      
      // Notificar a todos los clientes
      io.emit("agent-connected", agent);
      io.emit("agents-list", agents);
    });

    socket.on("agent-logout", (agentId) => {
      agents = agents.filter(a => a.id !== agentId);
      io.emit("agent-disconnected", agentId);
      io.emit("agents-list", agents);
    });

    // === MANEJO DE TAREAS ===
    socket.on("create-task", (taskData) => {
      const task = {
        ...taskData,
        id: taskData.id || Date.now().toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      tasks.push(task);
      console.log(`Nueva tarea creada: ${task.title}`);
      
      io.emit("task-created", task);
      io.emit("tasks-list", tasks);
    });

    socket.on("update-task", (taskData) => {
      const taskIndex = tasks.findIndex(t => t.id === taskData.id);
      if (taskIndex !== -1) {
        tasks[taskIndex] = {
          ...tasks[taskIndex],
          ...taskData,
          updatedAt: new Date()
        };
        
        console.log(`Tarea actualizada: ${tasks[taskIndex].title}`);
        
        io.emit("task-updated", tasks[taskIndex]);
        io.emit("tasks-list", tasks);
      }
    });

    socket.on("delete-task", ({ taskId, deletedBy }) => {
      const taskIndex = tasks.findIndex(t => t.id === taskId);
      if (taskIndex !== -1) {
        const deletedTask = tasks[taskIndex];
        tasks.splice(taskIndex, 1);
        
        console.log(`Tarea eliminada: ${deletedTask.title}`);
        
        io.emit("task-deleted", { taskId, deletedBy });
        io.emit("tasks-list", tasks);
      }
    });

    // === MANEJO DE DESCONEXIÓN ===
    socket.on("disconnect", () => {
      console.log(`Cliente desconectado: ${socket.id}`);
      
      // Remover agente de la lista si estaba conectado
      const disconnectedAgent = agents.find(a => a.socketId === socket.id);
      if (disconnectedAgent) {
        agents = agents.filter(a => a.socketId !== socket.id);
        io.emit("agent-disconnected", disconnectedAgent.id);
        io.emit("agents-list", agents);
        console.log(`Agente desconectado: ${disconnectedAgent.name}`);
      }
    });

    // === EVENTOS ADICIONALES ===
    socket.on("get-agent-stats", (agentId) => {
      const agentTasks = tasks.filter(t => 
        t.assignedTo === agentId || t.createdBy === agentId
      );
      
      const stats = {
        totalTasks: agentTasks.length,
        completedTasks: agentTasks.filter(t => t.status === 'completed').length,
        pendingTasks: agentTasks.filter(t => t.status === 'pending').length,
        inProgressTasks: agentTasks.filter(t => t.status === 'in-progress').length
      };
      
      socket.emit("agent-stats", { agentId, stats });
    });

    socket.on("assign-task", ({ taskId, agentId, assignedBy }) => {
      const taskIndex = tasks.findIndex(t => t.id === taskId);
      if (taskIndex !== -1) {
        tasks[taskIndex] = {
          ...tasks[taskIndex],
          assignedTo: agentId,
          assignedBy: assignedBy,
          assignedAt: new Date(),
          updatedAt: new Date()
        };
        
        io.emit("task-assigned", tasks[taskIndex]);
        io.emit("tasks-list", tasks);
      }
    });
  });

  console.log("Setting Up Socket.io with Agent Management");
  res.end();
}
