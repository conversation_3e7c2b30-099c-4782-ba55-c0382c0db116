import { useState } from 'react';
import { useApp } from '../context/AppContext';

export default function Notifications() {
  const { state, actions } = useApp();
  const { notifications } = state;
  const [isOpen, setIsOpen] = useState(false);

  const unreadCount = notifications.length;

  const handleRemoveNotification = (id) => {
    actions.removeNotification(id);
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      default: return '🔔';
    }
  };

  const formatTime = (timestamp) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    if (diff < 60000) return 'Ahora';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h`;
    return time.toLocaleDateString();
  };

  return (
    <div className="notifications-container">
      <button 
        className="notifications-trigger"
        onClick={() => setIsOpen(!isOpen)}
      >
        🔔
        {unreadCount > 0 && (
          <span className="notification-badge">{unreadCount}</span>
        )}
      </button>

      {isOpen && (
        <div className="notifications-dropdown">
          <div className="notifications-header">
            <h3>Notificaciones</h3>
            {unreadCount > 0 && (
              <button 
                className="clear-all-btn"
                onClick={() => notifications.forEach(n => handleRemoveNotification(n.id))}
              >
                Limpiar todo
              </button>
            )}
          </div>

          <div className="notifications-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                <span>📭</span>
                <p>No hay notificaciones</p>
              </div>
            ) : (
              notifications.map(notification => (
                <div 
                  key={notification.id} 
                  className={`notification-item ${notification.type}`}
                >
                  <div className="notification-content">
                    <span className="notification-icon">
                      {getNotificationIcon(notification.type)}
                    </span>
                    <div className="notification-text">
                      <p>{notification.message}</p>
                      <span className="notification-time">
                        {formatTime(notification.timestamp)}
                      </span>
                    </div>
                  </div>
                  <button 
                    className="remove-notification"
                    onClick={() => handleRemoveNotification(notification.id)}
                  >
                    ×
                  </button>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {isOpen && (
        <div 
          className="notifications-overlay"
          onClick={() => setIsOpen(false)}
        />
      )}

      <style jsx>{`
        .notifications-container {
          position: relative;
        }

        .notifications-trigger {
          position: relative;
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          padding: 0.5rem;
          border-radius: 50%;
          transition: background-color 0.3s;
        }

        .notifications-trigger:hover {
          background: #f8f9fa;
        }

        .notification-badge {
          position: absolute;
          top: 0;
          right: 0;
          background: #e74c3c;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          font-size: 0.7rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
        }

        .notifications-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 998;
        }

        .notifications-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          width: 350px;
          max-height: 400px;
          background: white;
          border-radius: 10px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          z-index: 999;
          overflow: hidden;
          margin-top: 0.5rem;
        }

        .notifications-header {
          padding: 1rem;
          border-bottom: 1px solid #eee;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .notifications-header h3 {
          margin: 0;
          color: #333;
          font-size: 1.1rem;
        }

        .clear-all-btn {
          background: none;
          border: none;
          color: #667eea;
          cursor: pointer;
          font-size: 0.9rem;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          transition: background-color 0.3s;
        }

        .clear-all-btn:hover {
          background: #f8f9fa;
        }

        .notifications-list {
          max-height: 300px;
          overflow-y: auto;
        }

        .no-notifications {
          padding: 2rem;
          text-align: center;
          color: #666;
        }

        .no-notifications span {
          font-size: 2rem;
          display: block;
          margin-bottom: 0.5rem;
        }

        .no-notifications p {
          margin: 0;
          font-size: 0.9rem;
        }

        .notification-item {
          display: flex;
          align-items: flex-start;
          padding: 1rem;
          border-bottom: 1px solid #f8f9fa;
          transition: background-color 0.3s;
        }

        .notification-item:hover {
          background: #f8f9fa;
        }

        .notification-item:last-child {
          border-bottom: none;
        }

        .notification-item.success {
          border-left: 3px solid #27ae60;
        }

        .notification-item.error {
          border-left: 3px solid #e74c3c;
        }

        .notification-item.warning {
          border-left: 3px solid #f39c12;
        }

        .notification-item.info {
          border-left: 3px solid #3498db;
        }

        .notification-content {
          display: flex;
          align-items: flex-start;
          gap: 0.75rem;
          flex: 1;
        }

        .notification-icon {
          font-size: 1.2rem;
          flex-shrink: 0;
        }

        .notification-text {
          flex: 1;
        }

        .notification-text p {
          margin: 0 0 0.25rem 0;
          color: #333;
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .notification-time {
          color: #999;
          font-size: 0.8rem;
        }

        .remove-notification {
          background: none;
          border: none;
          color: #999;
          cursor: pointer;
          font-size: 1.2rem;
          padding: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.3s;
          flex-shrink: 0;
        }

        .remove-notification:hover {
          background: #f8f9fa;
          color: #666;
        }

        @media (max-width: 480px) {
          .notifications-dropdown {
            width: 300px;
            right: -50px;
          }
        }
      `}</style>
    </div>
  );
}
