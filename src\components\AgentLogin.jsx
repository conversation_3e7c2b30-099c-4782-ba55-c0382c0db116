import { useState } from 'react';
import { useApp } from '../context/AppContext';

const agentRoles = [
  { id: 'coordinator', name: '<PERSON><PERSON><PERSON><PERSON>', color: '#e74c3c', icon: '👑' },
  { id: 'developer', name: 'Desarrollador', color: '#3498db', icon: '💻' },
  { id: 'tester', name: 'Tester', color: '#f39c12', icon: '🧪' },
  { id: 'analyst', name: '<PERSON><PERSON><PERSON>', color: '#9b59b6', icon: '📊' },
  { id: 'designer', name: '<PERSON><PERSON><PERSON><PERSON>', color: '#e67e22', icon: '🎨' },
  { id: 'manager', name: 'Manager', color: '#27ae60', icon: '📋' }
];

export default function AgentLogin() {
  const { actions } = useApp();
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    email: '',
    avatar: ''
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpiar error cuando el usuario empiece a escribir
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }
    
    if (!formData.role) {
      newErrors.role = 'Debe seleccionar un rol';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'El email es requerido';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const agent = {
      id: Date.now().toString(),
      name: formData.name.trim(),
      role: formData.role,
      email: formData.email.trim(),
      avatar: formData.avatar || getDefaultAvatar(formData.role),
      joinedAt: new Date(),
      status: 'online'
    };

    actions.loginAgent(agent);
  };

  const getDefaultAvatar = (role) => {
    const roleData = agentRoles.find(r => r.id === role);
    return roleData ? roleData.icon : '👤';
  };

  const selectedRole = agentRoles.find(r => r.id === formData.role);

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <h1>🚀 Sistema de Agentes</h1>
          <p>Ingresa como agente para coordinar tareas</p>
        </div>

        <form onSubmit={handleSubmit} className="login-form">
          <div className="form-group">
            <label htmlFor="name">Nombre del Agente</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Ej: Juan Pérez"
              className={errors.name ? 'error' : ''}
            />
            {errors.name && <span className="error-message">{errors.name}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className={errors.email ? 'error' : ''}
            />
            {errors.email && <span className="error-message">{errors.email}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="role">Rol del Agente</label>
            <div className="role-selector">
              {agentRoles.map(role => (
                <div
                  key={role.id}
                  className={`role-option ${formData.role === role.id ? 'selected' : ''}`}
                  onClick={() => setFormData(prev => ({ ...prev, role: role.id }))}
                  style={{ borderColor: formData.role === role.id ? role.color : '#ddd' }}
                >
                  <span className="role-icon">{role.icon}</span>
                  <span className="role-name">{role.name}</span>
                </div>
              ))}
            </div>
            {errors.role && <span className="error-message">{errors.role}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="avatar">Avatar (Emoji opcional)</label>
            <input
              type="text"
              id="avatar"
              name="avatar"
              value={formData.avatar}
              onChange={handleInputChange}
              placeholder={selectedRole ? selectedRole.icon : '👤'}
              maxLength="2"
            />
          </div>

          <button type="submit" className="login-button">
            Ingresar como {selectedRole ? selectedRole.name : 'Agente'}
          </button>
        </form>

        <div className="login-info">
          <h3>Roles disponibles:</h3>
          <ul>
            <li><strong>Coordinador:</strong> Asigna y supervisa tareas</li>
            <li><strong>Desarrollador:</strong> Implementa soluciones técnicas</li>
            <li><strong>Tester:</strong> Verifica calidad y funcionalidad</li>
            <li><strong>Analista:</strong> Analiza requerimientos y datos</li>
            <li><strong>Diseñador:</strong> Crea interfaces y experiencias</li>
            <li><strong>Manager:</strong> Gestiona proyectos y equipos</li>
          </ul>
        </div>
      </div>

      <style jsx>{`
        .login-container {
          min-height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }

        .login-card {
          background: white;
          border-radius: 15px;
          padding: 40px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 500px;
          width: 100%;
        }

        .login-header {
          text-align: center;
          margin-bottom: 30px;
        }

        .login-header h1 {
          color: #333;
          margin-bottom: 10px;
          font-size: 2.5em;
        }

        .login-header p {
          color: #666;
          font-size: 1.1em;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: #333;
        }

        .form-group input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 8px;
          font-size: 16px;
          transition: border-color 0.3s;
        }

        .form-group input:focus {
          outline: none;
          border-color: #667eea;
        }

        .form-group input.error {
          border-color: #e74c3c;
        }

        .error-message {
          color: #e74c3c;
          font-size: 14px;
          margin-top: 5px;
          display: block;
        }

        .role-selector {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 10px;
          margin-top: 10px;
        }

        .role-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 15px;
          border: 2px solid #ddd;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.3s;
          background: #f8f9fa;
        }

        .role-option:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .role-option.selected {
          background: #f0f8ff;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .role-icon {
          font-size: 2em;
          margin-bottom: 5px;
        }

        .role-name {
          font-weight: 600;
          color: #333;
          text-align: center;
        }

        .login-button {
          width: 100%;
          padding: 15px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          border-radius: 10px;
          font-size: 18px;
          font-weight: 600;
          cursor: pointer;
          transition: transform 0.3s;
        }

        .login-button:hover {
          transform: translateY(-2px);
        }

        .login-info {
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #eee;
        }

        .login-info h3 {
          color: #333;
          margin-bottom: 15px;
        }

        .login-info ul {
          list-style: none;
          padding: 0;
        }

        .login-info li {
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;
        }

        .login-info strong {
          color: #333;
        }
      `}</style>
    </div>
  );
}
